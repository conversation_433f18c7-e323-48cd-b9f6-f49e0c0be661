import zipfile
import os
from PIL import Image
from fpdf import FPDF
import pytesseract
import fitz  # PyMuPDF

# Set Tesseract path (uncomment and modify path if <PERSON><PERSON><PERSON> is not in PATH)
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def convert_zip_images_to_pdf(zip_path, output_pdf):
    extract_path = "extracted_images"
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

    image_files = sorted([
        os.path.join(extract_path, f)
        for f in os.listdir(extract_path)
        if f.lower().endswith(('.png', '.jpg', '.jpeg'))
    ])

    pdf = FPDF()
    for image in image_files:
        img = Image.open(image)
        width, height = img.size
        width, height = width * 0.264583, height * 0.264583
        pdf.add_page()
        pdf.image(image, 0, 0, width, height)
    pdf.output(output_pdf)
    return output_pdf

def extract_text_from_pdf(pdf_path, output_text):
    doc = fitz.open(pdf_path)
    text_data = ""
    for page_num, page in enumerate(doc):
        text = page.get_text()
        if not text.strip():  # if scanned, use OCR
            try:
                pix = page.get_pixmap()
                from io import BytesIO
                img = Image.open(BytesIO(pix.tobytes("png")))
                text = pytesseract.image_to_string(img)
            except Exception as e:
                text = f"[OCR Error: {str(e)}]"
                print(f"OCR failed for page {page_num + 1}: {e}")
        text_data += f"\n--- Page {page_num + 1} ---\n{text}"
    with open(output_text, "w", encoding="utf-8") as f:
        f.write(text_data)
    return output_text

# Example run
zip_file = "Screenshot 2025-07-07 171612.zip"
pdf_file = "output.pdf"
text_file = "output_text.txt"

pdf_path = convert_zip_images_to_pdf(zip_file, pdf_file)
text_path = extract_text_from_pdf(pdf_path, text_file)
print(f"PDF saved to: {pdf_path}")
print(f"Text saved to: {text_path}")
