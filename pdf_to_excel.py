import zipfile
import os
import re
from PIL import Image
from fpdf import FPDF
import pytesseract
import fitz  # PyMuPDF
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment

# Set Tesseract path (uncomment and modify path if <PERSON><PERSON><PERSON> is not in PATH)
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def convert_zip_images_to_pdf(zip_path, output_pdf):
    """Convert ZIP file containing images to PDF"""
    extract_path = "extracted_images"
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

    image_files = sorted([
        os.path.join(extract_path, f)
        for f in os.listdir(extract_path)
        if f.lower().endswith(('.png', '.jpg', '.jpeg'))
    ])

    pdf = FPDF()
    for image in image_files:
        img = Image.open(image)
        width, height = img.size
        width, height = width * 0.264583, height * 0.264583
        pdf.add_page()
        pdf.image(image, 0, 0, width, height)
    pdf.output(output_pdf)
    return output_pdf

def extract_text_from_pdf(pdf_path, output_text):
    """Extract text from PDF using OCR when needed"""
    doc = fitz.open(pdf_path)
    text_data = ""
    for page_num, page in enumerate(doc):
        text = page.get_text()
        if not text.strip():  # if scanned, use OCR
            try:
                pix = page.get_pixmap()
                from io import BytesIO
                img = Image.open(BytesIO(pix.tobytes("png")))
                text = pytesseract.image_to_string(img)
            except Exception as e:
                text = f"[OCR Error: {str(e)}]"
                print(f"OCR failed for page {page_num + 1}: {e}")
        text_data += f"\n--- Page {page_num + 1} ---\n{text}"
    
    with open(output_text, "w", encoding="utf-8") as f:
        f.write(text_data)
    return output_text

def extract_contact_info(text):
    """Extract contact information using regex patterns"""
    contacts = []
    
    # Split text into sections (assuming each contact is separated by page breaks or multiple newlines)
    sections = re.split(r'--- Page \d+ ---', text)
    
    for section in sections:
        if not section.strip() or '[OCR Error' in section:
            continue
            
        contact = {
            'Name': '',
            'Title': '',
            'Company': '',
            'Email': '',
            'Phone': '',
            'HQ': '',
            'Locations': ''
        }
        
        # Extract email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, section)
        contact['Email'] = ', '.join(emails) if emails else ''
        
        # Extract phone numbers (various formats)
        phone_pattern = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        phones = re.findall(phone_pattern, section)
        if phones:
            contact['Phone'] = ', '.join([f"({p[0]}) {p[1]}-{p[2]}" for p in phones])
        
        # Extract names (look for capitalized words, typically at the beginning)
        lines = [line.strip() for line in section.split('\n') if line.strip()]
        
        # Try to identify name (usually first meaningful line with capital letters)
        for line in lines[:5]:  # Check first 5 lines
            if re.match(r'^[A-Z][a-z]+ [A-Z][a-z]+', line.strip()):
                contact['Name'] = line.strip()
                break
        
        # Extract company names (look for common company indicators)
        company_indicators = ['Inc', 'LLC', 'Corp', 'Company', 'Ltd', 'Group', 'Solutions', 'Technologies']
        for line in lines:
            for indicator in company_indicators:
                if indicator.lower() in line.lower():
                    contact['Company'] = line.strip()
                    break
            if contact['Company']:
                break
        
        # Extract titles (look for common job titles)
        title_keywords = ['CEO', 'CTO', 'Manager', 'Director', 'President', 'Vice President', 'VP', 'Senior', 'Lead']
        for line in lines:
            for keyword in title_keywords:
                if keyword.lower() in line.lower():
                    contact['Title'] = line.strip()
                    break
            if contact['Title']:
                break
        
        # Extract locations/addresses (look for state abbreviations, zip codes)
        location_pattern = r'[A-Z]{2}\s+\d{5}(?:-\d{4})?'
        locations = re.findall(location_pattern, section)
        if locations:
            contact['Locations'] = ', '.join(locations)
        
        # Only add contact if we found at least name, email, or phone
        if contact['Name'] or contact['Email'] or contact['Phone']:
            contacts.append(contact)
    
    return contacts

def create_excel_file(contacts, output_file):
    """Create formatted Excel file with contact information"""
    # Create DataFrame
    df = pd.DataFrame(contacts)
    
    # Create Excel workbook with formatting
    wb = Workbook()
    ws = wb.active
    ws.title = "Contacts"
    
    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Write headers
    headers = ['Name', 'Title', 'Company', 'Email', 'Phone', 'HQ', 'Locations']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Write data
    for row, contact in enumerate(contacts, 2):
        for col, header in enumerate(headers, 1):
            ws.cell(row=row, column=col, value=contact.get(header, ''))
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save workbook
    wb.save(output_file)
    return output_file

def main():
    """Main function to process ZIP to Excel"""
    # File paths
    zip_file = "Screenshot 2025-07-07 171612.zip"
    pdf_file = "output.pdf"
    text_file = "output_text.txt"
    excel_file = "contacts.xlsx"
    
    print("Step 1: Converting ZIP to PDF...")
    pdf_path = convert_zip_images_to_pdf(zip_file, pdf_file)
    print(f"✓ PDF saved to: {pdf_path}")
    
    print("\nStep 2: Extracting text from PDF...")
    text_path = extract_text_from_pdf(pdf_path, text_file)
    print(f"✓ Text saved to: {text_path}")
    
    print("\nStep 3: Extracting contact information...")
    with open(text_file, 'r', encoding='utf-8') as f:
        text_content = f.read()
    
    contacts = extract_contact_info(text_content)
    print(f"✓ Found {len(contacts)} potential contacts")
    
    print("\nStep 4: Creating Excel file...")
    excel_path = create_excel_file(contacts, excel_file)
    print(f"✓ Excel file saved to: {excel_path}")
    
    # Display summary
    print(f"\n📊 Summary:")
    print(f"   • PDF: {pdf_file}")
    print(f"   • Text: {text_file}")
    print(f"   • Excel: {excel_file}")
    print(f"   • Contacts found: {len(contacts)}")

if __name__ == "__main__":
    main()
