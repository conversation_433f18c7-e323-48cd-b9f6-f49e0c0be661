import zipfile
import os
import re
from PIL import Image
from fpdf import FPDF
import pytesseract
import fitz  # PyMuPDF
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment

# Set Tesseract path (uncomment and modify path if <PERSON><PERSON><PERSON> is not in PATH)
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

def convert_zip_images_to_pdf(zip_path, output_pdf):
    """Convert ZIP file containing images to PDF"""
    extract_path = "extracted_images"
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

    image_files = sorted([
        os.path.join(extract_path, f)
        for f in os.listdir(extract_path)
        if f.lower().endswith(('.png', '.jpg', '.jpeg'))
    ])

    pdf = FPDF()
    for image in image_files:
        img = Image.open(image)
        width, height = img.size
        width, height = width * 0.264583, height * 0.264583
        pdf.add_page()
        pdf.image(image, 0, 0, width, height)
    pdf.output(output_pdf)
    return output_pdf

def extract_text_from_pdf(pdf_path, output_text):
    """Extract text from PDF using OCR when needed"""
    doc = fitz.open(pdf_path)
    text_data = ""
    for page_num, page in enumerate(doc):
        text = page.get_text()
        if not text.strip():  # if scanned, use OCR
            try:
                pix = page.get_pixmap()
                from io import BytesIO
                img = Image.open(BytesIO(pix.tobytes("png")))
                text = pytesseract.image_to_string(img)
            except Exception as e:
                text = f"[OCR Error: {str(e)}]"
                print(f"OCR failed for page {page_num + 1}: {e}")
        text_data += f"\n--- Page {page_num + 1} ---\n{text}"
    
    with open(output_text, "w", encoding="utf-8") as f:
        f.write(text_data)
    return output_text

def extract_contact_info(text):
    """Extract contact information from structured CSV-like text data"""
    contacts = []

    # First try to extract structured CSV-like data
    lines = text.split('\n')

    # Add header line if not present
    if not text.startswith('Name,Title,Company'):
        text = "Name,Title,Company,Email,Mobile Phone,Direct Phone,HQ Phone,Location,CRM\n" + text

    # Process each line
    for line in lines:
        line = line.strip()
        if not line or line.startswith('Name,Title,Company') or '[OCR Error' in line or '--- Page' in line:
            continue

        # Split by comma and handle quoted fields
        parts = []
        current_part = ""
        in_quotes = False

        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip().strip('"'))
                current_part = ""
            else:
                current_part += char

        # Don't forget the last part
        if current_part:
            parts.append(current_part.strip().strip('"'))

        # Only process lines with enough fields
        if len(parts) >= 3:  # At least name, title, company
            contact = {
                'Name': parts[0] if len(parts) > 0 else '',
                'Title': parts[1] if len(parts) > 1 else '',
                'Company': parts[2] if len(parts) > 2 else '',
                'Email': parts[3] if len(parts) > 3 else '',
                'Mobile Phone': parts[4] if len(parts) > 4 else '',
                'Direct Phone': parts[5] if len(parts) > 5 else '',
                'HQ Phone': parts[6] if len(parts) > 6 else '',
                'Location': parts[7] if len(parts) > 7 else '',
                'CRM': parts[8] if len(parts) > 8 else ''
            }

            # Only add if we have a name
            if contact['Name']:
                contacts.append(contact)

    # If no structured data found, try to extract from unstructured text
    if not contacts:
        # Split text into sections (assuming each contact is separated by page breaks or multiple newlines)
        sections = re.split(r'--- Page \d+ ---', text)

        for section in sections:
            if not section.strip() or '[OCR Error' in section:
                continue

            contact = {
                'Name': '',
                'Title': '',
                'Company': '',
                'Email': '',
                'Mobile Phone': '',
                'Direct Phone': '',
                'HQ Phone': '',
                'Location': '',
                'CRM': ''
            }

            # Extract email addresses
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, section)
            if emails:
                contact['Email'] = emails[0]

            # Extract phone numbers (various formats)
            phone_pattern = r'(?:\+?[0-9]{1,3}[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
            phones = re.findall(phone_pattern, section)

            if phones:
                if len(phones) >= 1:
                    contact['Mobile Phone'] = f"+({phones[0][0]}) {phones[0][1]}-{phones[0][2]}"
                if len(phones) >= 2:
                    contact['Direct Phone'] = f"+({phones[1][0]}) {phones[1][1]}-{phones[1][2]}"
                if len(phones) >= 3:
                    contact['HQ Phone'] = f"+({phones[2][0]}) {phones[2][1]}-{phones[2][2]}"

            # Extract names (look for capitalized words, typically at the beginning)
            lines = [line.strip() for line in section.split('\n') if line.strip()]

            # Try to identify name (usually first meaningful line with capital letters)
            for line in lines[:5]:  # Check first 5 lines
                if re.match(r'^[A-Z][a-z]+ [A-Z][a-z]+', line.strip()):
                    contact['Name'] = line.strip()
                    break

            # Extract titles (look for common job titles)
            title_keywords = ['CEO', 'CTO', 'Manager', 'Director', 'President', 'Vice President', 'VP', 'Senior', 'Lead', 'Founder']
            for line in lines:
                for keyword in title_keywords:
                    if keyword.lower() in line.lower():
                        contact['Title'] = line.strip()
                        break
                if contact['Title']:
                    break

            # Extract company names
            company_indicators = ['Inc', 'LLC', 'Corp', 'Company', 'Ltd', 'Group', 'Solutions', 'Technologies', 'Properties', 'Real Estate']
            for line in lines:
                for indicator in company_indicators:
                    if indicator.lower() in line.lower():
                        contact['Company'] = line.strip()
                        break
                if contact['Company']:
                    break

            # Extract locations
            location_indicators = ['Dubai', 'Abu Dhabi', 'UAE', 'United Arab Emirates', 'UK', 'United Kingdom']
            for line in lines:
                for indicator in location_indicators:
                    if indicator.lower() in line.lower():
                        contact['Location'] = line.strip()
                        break
                if contact['Location']:
                    break

            # Only add contact if we found at least name, email, or phone
            if contact['Name'] or contact['Email'] or contact['Mobile Phone']:
                contacts.append(contact)

    return contacts

def create_excel_file(contacts, output_file):
    """Create formatted Excel file with contact information"""
    # Create DataFrame
    df = pd.DataFrame(contacts)
    
    # Create Excel workbook with formatting
    wb = Workbook()
    ws = wb.active
    ws.title = "Contacts"
    
    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Write headers
    headers = ['Name', 'Title', 'Company', 'Email', 'Mobile Phone', 'Direct Phone', 'HQ Phone', 'Location', 'CRM']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Write data
    for row, contact in enumerate(contacts, 2):
        for col, header in enumerate(headers, 1):
            ws.cell(row=row, column=col, value=contact.get(header, ''))
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save workbook
    wb.save(output_file)
    return output_file

def main():
    """Main function to process ZIP to Excel"""
    # File paths
    zip_file = "Screenshot 2025-07-07 171612.zip"
    pdf_file = "output.pdf"
    text_file = "output_text.txt"
    excel_file = "contacts.xlsx"
    
    print("Step 1: Converting ZIP to PDF...")
    pdf_path = convert_zip_images_to_pdf(zip_file, pdf_file)
    print(f"✓ PDF saved to: {pdf_path}")
    
    print("\nStep 2: Extracting text from PDF...")
    text_path = extract_text_from_pdf(pdf_path, text_file)
    print(f"✓ Text saved to: {text_path}")
    
    print("\nStep 3: Extracting contact information...")
    with open(text_file, 'r', encoding='utf-8') as f:
        text_content = f.read()
    
    contacts = extract_contact_info(text_content)
    print(f"✓ Found {len(contacts)} potential contacts")
    
    print("\nStep 4: Creating Excel file...")
    excel_path = create_excel_file(contacts, excel_file)
    print(f"✓ Excel file saved to: {excel_path}")
    
    # Display summary
    print(f"\n📊 Summary:")
    print(f"   • PDF: {pdf_file}")
    print(f"   • Text: {text_file}")
    print(f"   • Excel: {excel_file}")
    print(f"   • Contacts found: {len(contacts)}")

if __name__ == "__main__":
    main()
