import pandas as pd

# Read the Excel file
df = pd.read_excel('contacts.xlsx')

print("📊 Excel File Preview:")
print("=" * 50)
print(f"Total rows: {len(df)}")
print(f"Columns: {list(df.columns)}")
print("\n🔍 First 5 rows:")
print(df.head().to_string())

print("\n📈 Data Summary:")
for col in df.columns:
    non_empty = df[col].notna().sum()
    print(f"  {col}: {non_empty}/{len(df)} entries ({non_empty/len(df)*100:.1f}%)")

print("\n💡 Note: Since Tesseract OCR is not installed, the data extraction")
print("   is limited. Install Tesseract for better text recognition!")
