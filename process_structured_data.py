import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON>, <PERSON><PERSON>Fill, Alignment

def process_structured_contacts(input_file, output_excel):
    """Process structured contact data and create Excel file"""
    
    # Read the text file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    contacts = []
    lines = content.strip().split('\n')
    
    for line in lines:
        if not line.strip():
            continue
            
        # Split by comma and handle quoted fields properly
        parts = []
        current_part = ""
        in_quotes = False
        
        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip().strip('"'))
                current_part = ""
            else:
                current_part += char
        
        # Don't forget the last part
        if current_part:
            parts.append(current_part.strip().strip('"'))
        
        # Create contact record
        if len(parts) >= 3:  # At least name, title, company
            contact = {
                'Name': parts[0] if len(parts) > 0 else '',
                'Title': parts[1] if len(parts) > 1 else '',
                'Company': parts[2] if len(parts) > 2 else '',
                'Email': parts[3] if len(parts) > 3 else '',
                'Mobile Phone': parts[4] if len(parts) > 4 else '',
                'Direct Phone': parts[5] if len(parts) > 5 else '',
                'HQ Phone': parts[6] if len(parts) > 6 else '',
                'Location': parts[7] if len(parts) > 7 else '',
                'CRM': parts[8] if len(parts) > 8 else ''
            }
            
            if contact['Name']:  # Only add if we have a name
                contacts.append(contact)
    
    # Create Excel file
    wb = Workbook()
    ws = wb.active
    ws.title = "Contacts"
    
    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Write headers
    headers = ['Name', 'Title', 'Company', 'Email', 'Mobile Phone', 'Direct Phone', 'HQ Phone', 'Location', 'CRM']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Write data
    for row, contact in enumerate(contacts, 2):
        for col, header in enumerate(headers, 1):
            ws.cell(row=row, column=col, value=contact.get(header, ''))
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save workbook
    wb.save(output_excel)
    
    return contacts, output_excel

# Process the sample data
print("🔄 Processing structured contact data...")
contacts, excel_file = process_structured_contacts('sample_contacts.txt', 'contacts_structured.xlsx')

print(f"✅ Successfully processed {len(contacts)} contacts")
print(f"📊 Excel file created: {excel_file}")

# Display first few contacts
print("\n📋 Sample contacts:")
for i, contact in enumerate(contacts[:3], 1):
    print(f"\n{i}. {contact['Name']}")
    print(f"   Title: {contact['Title']}")
    print(f"   Company: {contact['Company']}")
    print(f"   Email: {contact['Email']}")
    print(f"   Mobile: {contact['Mobile Phone']}")
    if contact['Location']:
        print(f"   Location: {contact['Location']}")

print(f"\n💡 To process your actual PDF text:")
print(f"   1. Install Tesseract OCR")
print(f"   2. Run: python pdf_to_excel.py")
print(f"   3. Or manually copy your structured data to a text file")
print(f"   4. Run this script on that file")
