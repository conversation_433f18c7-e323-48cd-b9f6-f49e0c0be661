import re
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON>, <PERSON><PERSON>Fill, Alignment

def extract_contact_info_improved(text):
    """Extract contact information from structured CSV-like text data"""
    contacts = []
    
    # Split text into lines
    lines = text.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('Name,Title,Company'):  # Skip header
            continue
        
        # Split by comma and handle quoted fields
        parts = []
        current_part = ""
        in_quotes = False
        
        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip().strip('"'))
                current_part = ""
            else:
                current_part += char
        
        # Don't forget the last part
        if current_part:
            parts.append(current_part.strip().strip('"'))
        
        # Only process lines with enough fields
        if len(parts) >= 4:
            contact = {
                'Name': parts[0] if len(parts) > 0 else '',
                'Title': parts[1] if len(parts) > 1 else '',
                'Company': parts[2] if len(parts) > 2 else '',
                'Email': parts[3] if len(parts) > 3 else '',
                'Mobile Phone': parts[4] if len(parts) > 4 else '',
                'Direct Phone': parts[5] if len(parts) > 5 else '',
                'HQ Phone': parts[6] if len(parts) > 6 else '',
                'Location': parts[7] if len(parts) > 7 else '',
                'CRM': parts[8] if len(parts) > 8 else ''
            }
            
            # Only add if we have a name
            if contact['Name']:
                contacts.append(contact)
    
    return contacts

def create_excel_file_improved(contacts, output_file):
    """Create formatted Excel file with contact information"""
    # Create DataFrame
    df = pd.DataFrame(contacts)
    
    # Create Excel workbook with formatting
    wb = Workbook()
    ws = wb.active
    ws.title = "Contacts"
    
    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Write headers
    headers = ['Name', 'Title', 'Company', 'Email', 'Mobile Phone', 'Direct Phone', 'HQ Phone', 'Location', 'CRM']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Write data
    for row, contact in enumerate(contacts, 2):
        for col, header in enumerate(headers, 1):
            ws.cell(row=row, column=col, value=contact.get(header, ''))
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save workbook
    wb.save(output_file)
    return output_file

# Test with your sample data
sample_data = """Name,Title,Company,Email,Mobile Phone,Direct Phone,HQ Phone,Location,CRM
Rami Tabbara,Co-Founder & Co-Chief Executive Officer,Stake,<EMAIL>,+97156 216 5461,,,"Unit 170 Ist FI DIFC Gate Ave, Dubai, UAE",No CRM contact or account owner
Shilpa V. Mahtani,Co-Founder & Managing Director,Bnbme,<EMAIL>,+97150 872 6270,+971 4 457 2012,+971 ********,,
Amit Masand,Co-Founder & Managing Director,Union Square House Real Estate,,+97155 2510503,,+971 ********,"202 Bldg 6 Bay Sq, Dubai, UAE",
Talat Khurshid Rathore,Co-Founder & Director,Sky Links Real Estate,<EMAIL>,+97150 199 9679,,(*************,"Office 2705, Silver Tower Business Bay, Dubai, UAE",
Manar Mahmassani,Co-Founder,Stake,<EMAIL>,+971 4 509 0700,,,"Abu Dhabi, UAE","""

print("🧪 Testing improved contact extraction...")
contacts = extract_contact_info_improved(sample_data)

print(f"✓ Extracted {len(contacts)} contacts")
for i, contact in enumerate(contacts[:3], 1):
    print(f"\n📋 Contact {i}:")
    for key, value in contact.items():
        if value:
            print(f"   {key}: {value}")

print(f"\n📊 Creating test Excel file...")
excel_file = create_excel_file_improved(contacts, "contacts_corrected.xlsx")
print(f"✓ Excel file saved: {excel_file}")
